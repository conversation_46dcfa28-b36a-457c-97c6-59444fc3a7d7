FROM node:19-alpine

WORKDIR /app

# Copia package.json e yarn.lock (ou package-lock.json) para cache de dependências
COPY frontend/package.json frontend/yarn.lock ./

# Instala as dependências do projeto
RUN yarn install --frozen-lockfile

# Copia o restante do código da aplicação
COPY frontend .

# Expõe a porta que o React dev server ou a aplicação construída irá usar
EXPOSE 3000

# Comando para iniciar a aplicação em ambiente de desenvolvimento
CMD ["yarn", "start"]

# Para ambiente de produção (construir e servir com Nginx, por exemplo)
# FROM node:19-alpine as builder
# WORKDIR /app
# COPY frontend/package.json frontend/yarn.lock ./
# RUN yarn install --frozen-lockfile
# COPY frontend .
# RUN yarn build

# FROM nginx:alpine
# COPY --from=builder /app/build /usr/share/nginx/html
# EXPOSE 80
# CMD ["nginx", "-g", "daemon off;"]