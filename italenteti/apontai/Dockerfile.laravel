FROM php:8.3-fpm-alpine

# Argumento para o ambiente (desenvolvimento/produção)
ARG APP_ENV=local

# Instalação de dependências do sistema
RUN apk add --no-cache \
    git \
    unzip \
    libpq-dev \
    supervisor \
    rabbitmq-c-dev # Para a extensão amqp

# Instalação das extensões PHP necessárias
# pdo_pgsql: Para PostgreSQL
# bcmath: Comum em muitas aplicações Laravel
# pcntl: Essencial para rodar comandos CLI e workers (como o Swoole ou listeners de fila)
# amqp: Para comunicação com RabbitMQ, essencial para CQRS via mensagens
RUN docker-php-ext-install pdo_pgsql bcmath pcntl

# Instalação e configuração do Swoole
RUN pecl install swoole \
    && docker-php-ext-enable swoole

# Instalação da extensão AMQP para RabbitMQ
RUN pecl install amqp \
    && docker-php-ext-enable amqp

# Composer
COPY --from=composer:latest /usr/bin/composer /usr/local/bin/composer

WORKDIR /var/www/html

# Copia o composer.json e composer.lock para cache de dependências
COPY service-api/composer.json service-api/composer.lock ./
RUN composer install --no-dev --optimize-autoloader

# Copia o restante do código da aplicação Laravel
COPY service-api .

# Permissões
RUN chown -R www-data:www-data /var/www/html/storage \
    && chown -R www-data:www-data /var/www/html/bootstrap/cache

# Copia a configuração do Supervisor
COPY supervisor.conf /etc/supervisor/conf.d/supervisor.conf

EXPOSE 8000

# Comando padrão para iniciar o Supervisor
CMD ["supervisord", "-n"]