services:
  apontai-db:
    image: postgres:17
    container_name: apontai-db
    restart: always
    environment:
      POSTGRES_DB: apontai-db 
      POSTGRES_USER: seu_usuario
      POSTGRES_PASSWORD: sua_senha
    volumes:
      - db_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    networks:
      - app_network

  apontai-api:
    build:
      context: ./api
      dockerfile: Dockerfile.laravel
    container_name: apontai-api
    restart: always
    env_file:
      - ./api/.env
    volumes:
      - ./api:/var/www/html # Mapeia o código da sua API
    ports:
      - "8000:8000" # Porta que o Swoole irá escutar (exemplo)
    depends_on:
      - db
      - redis
      - rabbitmq
    networks:
      - app_network

  # Serviço React
  apontai-front:
    build:
      context: ./front # Caminho para a pasta da sua aplicação React
      dockerfile: Dockerfile.react
    container_name: react_app
    restart: always
    volumes:
      - ./front:/app # Mapeia o código do seu frontend
      - /app/node_modules # Evita que o node_modules local sobrescreva o do container
    ports:
      - "3000:3000" # Porta padrão do React Dev Server (exemplo)
    depends_on:
      - service_api # O frontend pode depender da API
    networks:
      - app_network

  # Serviço RabbitMQ
  apontai-queue:
    image: rabbitmq:4.1.2-management # Inclui o painel de gerenciamento
    container_name: apontai-queue
    restart: always
    ports:
      - "5672:5672" # Porta padrão do RabbitMQ
      - "15672:15672" # Porta do painel de gerenciamento
    environment:
      RABBITMQ_DEFAULT_USER: guest # Altere para seu usuário
      RABBITMQ_DEFAULT_PASS: guest # Altere para sua senha
    volumes:
      - apontai-queue_data:/var/lib/rabbitmq
    networks:
      - app_network

  # Serviço Redis
  apontai-cache:
    image: redis:8
    container_name: apontai-cache
    restart: always
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    networks:
      - app_network

volumes:
  db_data:
  redis_data:

networks:
  app_network:
    driver: bridge