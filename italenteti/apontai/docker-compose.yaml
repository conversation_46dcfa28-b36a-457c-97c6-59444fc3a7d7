services:
  apontai-db:
    image: postgres:17
    container_name: apontai-db
    restart: always
    environment:
      POSTGRES_DB: apontai-db 
      POSTGRES_USER: seu_usuario
      POSTGRES_PASSWORD: sua_senha
    volumes:
      - apontai-db_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    networks:
      - apontai-network

  apontai-api:
    build:
      context: ./api
      dockerfile: ../Dockerfile.laravel
    container_name: apontai-api
    restart: always
    env_file:
      - ./api/.env
    volumes:
      - ./api:/var/www/html
    ports:
      - "8000:8000"
    depends_on:
      - apontai-db
      - apontai-cache
      - apontai-queue
    networks:
      - apontai-network

  apontai-front:
    build:
      context: ./front
      dockerfile: Dockerfile.react
    container_name: react_app
    restart: always
    volumes:
      - ./front:/app
      - /app/node_modules
    ports:
      - "3000:3000"
    depends_on:
      - apontai-api
    networks:
      - apontai-network

  apontai-queue:
    image: rabbitmq:4.1.2-management
    container_name: apontai-queue
    restart: always
    ports:
      - "5672:5672" 
      - "15672:15672" 
    environment:
      RABBITMQ_DEFAULT_USER: guest
      RABBITMQ_DEFAULT_PASS: guest
    volumes:
      - apontai-queue_data:/var/lib/rabbitmq
    networks:
      - apontai-network

  # Serviço Redis
  apontai-cache:
    image: redis:8
    container_name: apontai-cache
    restart: always
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes
    volumes:
      - apontai-cache_data:/data
    networks:
      - apontai-network

volumes:
  apontai-db_data:
  apontai-cache_data:
  apontai-queue_data:

networks:
  apontai-network:
    driver: bridge